#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class XDFOnlineReporter:
    """
    新东方在线平台特定的上报逻辑
    负责将新东方在线用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "xdf_online"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报新东方在线登录cookies
        
        Args:
            cookies: 新东方在线cookies
            username: 新东方在线用户名
        """
        Reporter.report_cookies(XDFOnlineReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报新东方在线课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(XDFOnlineReporter.PLATFORM_CODE, courses) 