#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class QihangReporter:
    """
    启航教育平台特定的上报逻辑
    负责将启航教育用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "qihang"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报启航教育登录cookies
        
        Args:
            cookies: 启航教育cookies
            username: 启航教育用户名
        """
        Reporter.report_cookies(QihangReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报启航教育课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(QihangReporter.PLATFORM_CODE, courses) 