([('课程猫.exe', 'O:\\桌面\\备份信息\\批量下载器-2(备份)\\build\\课程猫\\课程猫.exe', 'EXECUTABLE'),
  ('playwright\\driver\\node.exe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\node.exe',
   'BINARY'),
  ('python39.dll', 'C:\\Program Files\\Python39\\python39.dll', 'BINARY'),
  ('scipy.libs\\libopenblas_v0.3.27--3aa239bc726cfb0bd8e5330d8d4c15c6.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy.libs\\libopenblas_v0.3.27--3aa239bc726cfb0bd8e5330d8d4c15c6.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('select.pyd', 'C:\\Program Files\\Python39\\DLLs\\select.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python39\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python39\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python39\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python39\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python39\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python39\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python39\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\greenlet\\_greenlet.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\hashing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\period.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\writers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslib.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\properties.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\ops.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\parsers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\json.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\window\\indexers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\groupby.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\interval.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\fields.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\index.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\hashtable.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\algos.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\arrays.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\join.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\_lib\\_ccallback_c.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\_philox.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\random\\_common.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_fblas.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_flapack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\cython_lapack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\cython_blas.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_decomp_update.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\special\\_ellip_harm_2.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_constants.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_highs\\_highs_constants.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_wrapper.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_highs\\_highs_wrapper.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_direct.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_qmc_cy.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_sobol.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\spatial\\_distance_pybind.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\spatial\\_hausdorff.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\spatial\\_distance_wrap.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\interpolate\\_rgi_cython.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\interpnd.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\interpolate\\interpnd.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\interpolate\\_ppoly.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\interpolate\\_bspl.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\dfitpack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\interpolate\\dfitpack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\interpolate\\_fitpack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_unuran\\unuran_wrapper.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_unuran\\unuran_wrapper.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_stats_pythran.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_rcont\\rcont.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_mvn.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\ndimage\\_nd_image.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\ndimage\\_ni_label.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_biasedurn.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\invgauss_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\invgauss_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\skewnorm_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\skewnorm_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nct_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\nct_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncx2_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\ncx2_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncf_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\ncf_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\hypergeom_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\hypergeom_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nbinom_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\nbinom_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\binom_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\binom_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\beta_ufunc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_boost\\beta_ufunc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\stats\\_stats.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\special\\cython_special.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\spatial\\transform\\_rotation.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\spatial\\_voronoi.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\spatial\\_qhull.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\spatial\\_ckdtree.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_pava_pybind.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_lsap.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_interpolative.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_interpolative.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_bglu_dense.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_slsqp.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_zeros.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_minpack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_cobyla.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_moduleTNC.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_lbfgsb.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack2.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_minpack2.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\optimize\\_group_columns.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\integrate\\_lsoda.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\integrate\\_dop.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\integrate\\_vode.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\integrate\\_quadpack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\integrate\\_odepack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\special\\_comb.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\special\\_specfun.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\special\\_ufuncs.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_cdflib.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\special\\_cdflib.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\special\\_ufuncs_cxx.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\_csparsetools.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\sparse\\_sparsetools.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\charset_normalizer\\md.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\_lib\\_fpumode.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy\\_lib\\messagestream.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\yaml\\_yaml.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\sparse.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\missing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\internals.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\ops_dispatch.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\indexing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\lib.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\reshape.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\pandas_datetime.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\pandas_parser.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\window\\aggregations.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\tslibs\\base.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\testing.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\sas.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\_libs\\byteswap.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\etree.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\_elementpath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\sax.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\objectify.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\html\\diff.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\html\\_difflib.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\builder.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('backports\\_datetime_fromisoformat.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\backports\\_datetime_fromisoformat.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\PIL\\_imagingft.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\PIL\\_imaging.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\PIL\\_webp.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\PIL\\_imagingtk.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\PIL\\_imagingcms.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\PIL\\_imagingmath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\yarl\\_quoting_c.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('propcache\\_helpers_c.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\propcache\\_helpers_c.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\multidict\\_multidict.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\aiohttp\\_http_writer.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\aiohttp\\_http_parser.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\frozenlist\\_frozenlist.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\mask.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\aiohttp\\_websocket\\mask.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\reader_c.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\aiohttp\\_websocket\\reader_c.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Program Files\\Python39\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('simplejson\\_speedups.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\simplejson\\_speedups.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python39\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Program Files\\Python39\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll', 'C:\\Program Files\\Python39\\DLLs\\libffi-7.dll', 'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Program Files\\Python39\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python39\\DLLs\\sqlite3.dll', 'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python39\\python3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python39\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('tcl86t.dll', 'C:\\Program Files\\Python39\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Program Files\\Python39\\DLLs\\tk86t.dll', 'BINARY'),
  ('config.json', 'O:\\桌面\\备份信息\\批量下载器-2(备份)\\config.json', 'DATA'),
  ('core\\__init__.py', 'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__init__.py', 'DATA'),
  ('core\\__pycache__\\__init__.cpython-311.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('core\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('core\\__pycache__\\auth.cpython-311.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\auth.cpython-311.pyc',
   'DATA'),
  ('core\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('core\\__pycache__\\base_downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\base_downloader.cpython-39.pyc',
   'DATA'),
  ('core\\__pycache__\\display.cpython-311.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\display.cpython-311.pyc',
   'DATA'),
  ('core\\__pycache__\\display.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\display.cpython-39.pyc',
   'DATA'),
  ('core\\__pycache__\\logger.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\logger.cpython-39.pyc',
   'DATA'),
  ('core\\__pycache__\\utils.cpython-311.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\utils.cpython-311.pyc',
   'DATA'),
  ('core\\__pycache__\\utils.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\__pycache__\\utils.cpython-39.pyc',
   'DATA'),
  ('core\\auth.py', 'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\auth.py', 'DATA'),
  ('core\\base_downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\base_downloader.py',
   'DATA'),
  ('core\\display.py', 'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\display.py', 'DATA'),
  ('core\\logger.py', 'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\logger.py', 'DATA'),
  ('core\\utils.py', 'O:\\桌面\\备份信息\\批量下载器-2(备份)\\core\\utils.py', 'DATA'),
  ('platforms\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\__init__.py',
   'DATA'),
  ('platforms\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\__pycache__\\pyinstaller_fix.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\__pycache__\\pyinstaller_fix.cpython-39.pyc',
   'DATA'),
  ('platforms\\acfun\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\__init__.py',
   'DATA'),
  ('platforms\\acfun\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\acfun\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\acfun\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\acfun\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\acfun\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\acfun\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\auth.py',
   'DATA'),
  ('platforms\\acfun\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\course.py',
   'DATA'),
  ('platforms\\acfun\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\downloader.py',
   'DATA'),
  ('platforms\\acfun\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\acfun\\main.py',
   'DATA'),
  ('platforms\\aixuetang\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__init__.py',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\test_browser.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\test_browser.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\test_browser_login.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\test_browser_login.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\test_course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\test_course.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\test_known_cookies.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\test_known_cookies.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\__pycache__\\test_playwright.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\__pycache__\\test_playwright.cpython-39.pyc',
   'DATA'),
  ('platforms\\aixuetang\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\auth.py',
   'DATA'),
  ('platforms\\aixuetang\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\course.py',
   'DATA'),
  ('platforms\\aixuetang\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\downloader.py',
   'DATA'),
  ('platforms\\aixuetang\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\aixuetang\\main.py',
   'DATA'),
  ('platforms\\bilibili\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\__init__.py',
   'DATA'),
  ('platforms\\bilibili\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\auth.py',
   'DATA'),
  ('platforms\\bilibili\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\course.py',
   'DATA'),
  ('platforms\\bilibili\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili\\downloader.py',
   'DATA'),
  ('platforms\\bilibili_up\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\__init__.py',
   'DATA'),
  ('platforms\\bilibili_up\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili_up\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili_up\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili_up\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili_up\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\bilibili_up\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\auth.py',
   'DATA'),
  ('platforms\\bilibili_up\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\course.py',
   'DATA'),
  ('platforms\\bilibili_up\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\downloader.py',
   'DATA'),
  ('platforms\\bilibili_up\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\bilibili_up\\main.py',
   'DATA'),
  ('platforms\\cctalk\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\__init__.py',
   'DATA'),
  ('platforms\\cctalk\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\cctalk\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\cctalk\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\cctalk\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\cctalk\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\auth.py',
   'DATA'),
  ('platforms\\cctalk\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\course.py',
   'DATA'),
  ('platforms\\cctalk\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cctalk\\downloader.py',
   'DATA'),
  ('platforms\\chaoxing\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\__init__.py',
   'DATA'),
  ('platforms\\chaoxing\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\chaoxing\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\chaoxing\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\chaoxing\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\chaoxing\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\chaoxing\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\auth.py',
   'DATA'),
  ('platforms\\chaoxing\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\course.py',
   'DATA'),
  ('platforms\\chaoxing\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\downloader.py',
   'DATA'),
  ('platforms\\chaoxing\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\chaoxing\\main.py',
   'DATA'),
  ('platforms\\cqtbb\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cqtbb\\__init__.py',
   'DATA'),
  ('platforms\\cqtbb\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cqtbb\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\cqtbb\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cqtbb\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\cqtbb\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\cqtbb\\main.py',
   'DATA'),
  ('platforms\\dlbfz\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\dlbfz\\__init__.py',
   'DATA'),
  ('platforms\\dlbfz\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\dlbfz\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\dlbfz\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\dlbfz\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\dlbfz\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\dlbfz\\main.py',
   'DATA'),
  ('platforms\\gaodun\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\__init__.py',
   'DATA'),
  ('platforms\\gaodun\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaodun\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaodun\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaodun\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaodun\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\auth.py',
   'DATA'),
  ('platforms\\gaodun\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\course.py',
   'DATA'),
  ('platforms\\gaodun\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaodun\\downloader.py',
   'DATA'),
  ('platforms\\gaotu\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\__init__.py',
   'DATA'),
  ('platforms\\gaotu\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaotu\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaotu\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaotu\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaotu\\__pycache__\\material.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\__pycache__\\material.cpython-39.pyc',
   'DATA'),
  ('platforms\\gaotu\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\auth.py',
   'DATA'),
  ('platforms\\gaotu\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\course.py',
   'DATA'),
  ('platforms\\gaotu\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\downloader.py',
   'DATA'),
  ('platforms\\gaotu\\material.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gaotu\\material.py',
   'DATA'),
  ('platforms\\gkzy\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gkzy\\__init__.py',
   'DATA'),
  ('platforms\\gkzy\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gkzy\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\gkzy\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gkzy\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\gkzy\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gkzy\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\gkzy\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gkzy\\auth.py',
   'DATA'),
  ('platforms\\gkzy\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gkzy\\main.py',
   'DATA'),
  ('platforms\\gtgz\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gtgz\\__init__.py',
   'DATA'),
  ('platforms\\gtgz\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gtgz\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\gtgz\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gtgz\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\gtgz\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gtgz\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\gtgz\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gtgz\\auth.py',
   'DATA'),
  ('platforms\\gtgz\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\gtgz\\main.py',
   'DATA'),
  ('platforms\\haoke\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\haoke\\__init__.py',
   'DATA'),
  ('platforms\\haoke\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\haoke\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\haoke\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\haoke\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\haoke\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\haoke\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\haoke\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\haoke\\auth.py',
   'DATA'),
  ('platforms\\haoke\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\haoke\\main.py',
   'DATA'),
  ('platforms\\hjb\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hjb\\__init__.py',
   'DATA'),
  ('platforms\\hjb\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hjb\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\hjb\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hjb\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\hjb\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hjb\\auth.py',
   'DATA'),
  ('platforms\\hjb\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hjb\\main.py',
   'DATA'),
  ('platforms\\hklh\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hklh\\__init__.py',
   'DATA'),
  ('platforms\\hklh\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hklh\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\hklh\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hklh\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\hklh\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hklh\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\hklh\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hklh\\auth.py',
   'DATA'),
  ('platforms\\hklh\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\hklh\\main.py',
   'DATA'),
  ('platforms\\kmzx\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\kmzx\\__init__.py',
   'DATA'),
  ('platforms\\kmzx\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\kmzx\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\kmzx\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\kmzx\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\kmzx\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\kmzx\\main.py',
   'DATA'),
  ('platforms\\linghang\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\linghang\\__init__.py',
   'DATA'),
  ('platforms\\linghang\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\linghang\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\linghang\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\linghang\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\linghang\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\linghang\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\linghang\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\linghang\\auth.py',
   'DATA'),
  ('platforms\\linghang\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\linghang\\main.py',
   'DATA'),
  ('platforms\\lzwk\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\lzwk\\__init__.py',
   'DATA'),
  ('platforms\\lzwk\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\lzwk\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\lzwk\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\lzwk\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\lzwk\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\lzwk\\main.py',
   'DATA'),
  ('platforms\\msttcz\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttcz\\__init__.py',
   'DATA'),
  ('platforms\\msttcz\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttcz\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\msttcz\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttcz\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\msttcz\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttcz\\main.py',
   'DATA'),
  ('platforms\\msttgz\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttgz\\__init__.py',
   'DATA'),
  ('platforms\\msttgz\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttgz\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\msttgz\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttgz\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\msttgz\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\msttgz\\main.py',
   'DATA'),
  ('platforms\\netease\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\__init__.py',
   'DATA'),
  ('platforms\\netease\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\netease\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\netease\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\netease\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\netease\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\auth.py',
   'DATA'),
  ('platforms\\netease\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\course.py',
   'DATA'),
  ('platforms\\netease\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\netease\\downloader.py',
   'DATA'),
  ('platforms\\pxxsx\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\pxxsx\\__init__.py',
   'DATA'),
  ('platforms\\pxxsx\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\pxxsx\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\pxxsx\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\pxxsx\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\pxxsx\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\pxxsx\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\pxxsx\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\pxxsx\\downloader.py',
   'DATA'),
  ('platforms\\pxxsx\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\pxxsx\\main.py',
   'DATA'),
  ('platforms\\qihang\\QhMain.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\QhMain.py',
   'DATA'),
  ('platforms\\qihang\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\__init__.py',
   'DATA'),
  ('platforms\\qihang\\__pycache__\\QhMain.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\__pycache__\\QhMain.cpython-39.pyc',
   'DATA'),
  ('platforms\\qihang\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\qihang\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\qihang\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\qihang\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\qihang\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\qihang\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\qihang\\downloader.py',
   'DATA'),
  ('platforms\\ql\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ql\\__init__.py',
   'DATA'),
  ('platforms\\ql\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ql\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\ql\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ql\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\ql\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ql\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\ql\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ql\\auth.py',
   'DATA'),
  ('platforms\\ql\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ql\\main.py',
   'DATA'),
  ('platforms\\shenyiedu\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\__init__.py',
   'DATA'),
  ('platforms\\shenyiedu\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\shenyiedu\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\shenyiedu\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\shenyiedu\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\shenyiedu\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\shenyiedu\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\auth.py',
   'DATA'),
  ('platforms\\shenyiedu\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\course.py',
   'DATA'),
  ('platforms\\shenyiedu\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\downloader.py',
   'DATA'),
  ('platforms\\shenyiedu\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\shenyiedu\\main.py',
   'DATA'),
  ('platforms\\sndd\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\sndd\\__init__.py',
   'DATA'),
  ('platforms\\sndd\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\sndd\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\sndd\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\sndd\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\sndd\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\sndd\\main.py',
   'DATA'),
  ('platforms\\trjy\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\trjy\\__init__.py',
   'DATA'),
  ('platforms\\trjy\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\trjy\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\trjy\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\trjy\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\trjy\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\trjy\\main.py',
   'DATA'),
  ('platforms\\ttkt\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ttkt\\__init__.py',
   'DATA'),
  ('platforms\\ttkt\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ttkt\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\ttkt\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ttkt\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\ttkt\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ttkt\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\ttkt\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ttkt\\auth.py',
   'DATA'),
  ('platforms\\ttkt\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ttkt\\main.py',
   'DATA'),
  ('platforms\\xdf\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf\\auth.py',
   'DATA'),
  ('platforms\\xdf\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf\\course.py',
   'DATA'),
  ('platforms\\xdf\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf\\downloader.py',
   'DATA'),
  ('platforms\\xdf_online\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\__init__.py',
   'DATA'),
  ('platforms\\xdf_online\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf_online\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf_online\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf_online\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf_online\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\xdf_online\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\auth.py',
   'DATA'),
  ('platforms\\xdf_online\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\course.py',
   'DATA'),
  ('platforms\\xdf_online\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\downloader.py',
   'DATA'),
  ('platforms\\xdf_online\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\main.py',
   'DATA'),
  ('platforms\\xdf_online\\新东方在线开发.md',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xdf_online\\新东方在线开发.md',
   'DATA'),
  ('platforms\\xiaoe\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\__init__.py',
   'DATA'),
  ('platforms\\xiaoe\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\auth.py',
   'DATA'),
  ('platforms\\xiaoe\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\course.py',
   'DATA'),
  ('platforms\\xiaoe\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\downloader.py',
   'DATA'),
  ('platforms\\xiaoe\\logs\\xiaoe_20250407_144907.log',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\logs\\xiaoe_20250407_144907.log',
   'DATA'),
  ('platforms\\xiaoe\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\main.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\__init__.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\__pycache__\\course_list.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\__pycache__\\course_list.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\__pycache__\\text.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\__pycache__\\text.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\__pycache__\\utils.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\__pycache__\\utils.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\auth.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__init__.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\base.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\base.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\big_column.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\big_column.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\camp.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\camp.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\camp_pro.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\camp_pro.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\column.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\column.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\default.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\default.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\ebook.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\ebook.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\live.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\live.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\member.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\member.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\__pycache__\\single.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\__pycache__\\single.cpython-39.pyc',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\base.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\base.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\big_column.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\big_column.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\camp.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\camp.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\camp_pro.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\camp_pro.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\column.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\column.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\default.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\default.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\ebook.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\ebook.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\live.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\live.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\member.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\member.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\catalog\\single.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\catalog\\single.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\course_list.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\course_list.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\downloader.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\text.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\text.py',
   'DATA'),
  ('platforms\\xiaoe\\modules\\utils.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xiaoe\\modules\\utils.py',
   'DATA'),
  ('platforms\\xueersi\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\__init__.py',
   'DATA'),
  ('platforms\\xueersi\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xueersi\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\xueersi\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\xueersi\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\xueersi\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\xueersi\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\auth.py',
   'DATA'),
  ('platforms\\xueersi\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\course.py',
   'DATA'),
  ('platforms\\xueersi\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\downloader.py',
   'DATA'),
  ('platforms\\xueersi\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xueersi\\main.py',
   'DATA'),
  ('platforms\\xwx\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwx\\__init__.py',
   'DATA'),
  ('platforms\\xwx\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwx\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xwx\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwx\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\xwx\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwx\\main.py',
   'DATA'),
  ('platforms\\xwxsy\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwxsy\\__init__.py',
   'DATA'),
  ('platforms\\xwxsy\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwxsy\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xwxsy\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwxsy\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\xwxsy\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwxsy\\main.py',
   'DATA'),
  ('platforms\\xwyk\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwyk\\__init__.py',
   'DATA'),
  ('platforms\\xwyk\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwyk\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\xwyk\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwyk\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\xwyk\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\xwyk\\main.py',
   'DATA'),
  ('platforms\\yangcong\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\__init__.py',
   'DATA'),
  ('platforms\\yangcong\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\yangcong\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\yangcong\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\yangcong\\__pycache__\\decrypt.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\__pycache__\\decrypt.cpython-39.pyc',
   'DATA'),
  ('platforms\\yangcong\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\yangcong\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\yangcong\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\auth.py',
   'DATA'),
  ('platforms\\yangcong\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\course.py',
   'DATA'),
  ('platforms\\yangcong\\decrypt.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\decrypt.py',
   'DATA'),
  ('platforms\\yangcong\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\downloader.py',
   'DATA'),
  ('platforms\\yangcong\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\main.py',
   'DATA'),
  ('platforms\\yangcong\\requirements.txt',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yangcong\\requirements.txt',
   'DATA'),
  ('platforms\\ydls\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ydls\\__init__.py',
   'DATA'),
  ('platforms\\ydls\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ydls\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\ydls\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ydls\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\ydls\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ydls\\downloader.py',
   'DATA'),
  ('platforms\\ydls\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ydls\\main.py',
   'DATA'),
  ('platforms\\ydls\\ydls.log',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\ydls\\ydls.log',
   'DATA'),
  ('platforms\\youdao\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\__init__.py',
   'DATA'),
  ('platforms\\youdao\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\youdao\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\youdao\\__pycache__\\course.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\__pycache__\\course.cpython-39.pyc',
   'DATA'),
  ('platforms\\youdao\\__pycache__\\downloader.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\__pycache__\\downloader.cpython-39.pyc',
   'DATA'),
  ('platforms\\youdao\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\youdao\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\auth.py',
   'DATA'),
  ('platforms\\youdao\\course.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\course.py',
   'DATA'),
  ('platforms\\youdao\\downloader.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\downloader.py',
   'DATA'),
  ('platforms\\youdao\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\youdao\\main.py',
   'DATA'),
  ('platforms\\yyx\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yyx\\__init__.py',
   'DATA'),
  ('platforms\\yyx\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yyx\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\yyx\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yyx\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\yyx\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\yyx\\main.py',
   'DATA'),
  ('platforms\\zuoyebang_student\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\zuoyebang_student\\__init__.py',
   'DATA'),
  ('platforms\\zuoyebang_student\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\zuoyebang_student\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('platforms\\zuoyebang_student\\__pycache__\\auth.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\zuoyebang_student\\__pycache__\\auth.cpython-39.pyc',
   'DATA'),
  ('platforms\\zuoyebang_student\\__pycache__\\main.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\zuoyebang_student\\__pycache__\\main.cpython-39.pyc',
   'DATA'),
  ('platforms\\zuoyebang_student\\auth.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\zuoyebang_student\\auth.py',
   'DATA'),
  ('platforms\\zuoyebang_student\\main.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\platforms\\zuoyebang_student\\main.py',
   'DATA'),
  ('reporters\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\__init__.py',
   'DATA'),
  ('reporters\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('reporters\\__pycache__\\reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\__pycache__\\reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__init__.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__init__.py',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\__init__.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\aixuetang_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\aixuetang_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\bilibili_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\bilibili_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\cctalk_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\cctalk_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\gaotu_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\gaotu_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\gtgz_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\gtgz_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\haoke_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\haoke_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\hjb_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\hjb_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\hklh_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\hklh_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\kmzx_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\kmzx_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\linghang_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\linghang_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\msttcz_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\msttcz_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\msttgz_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\msttgz_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\netease_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\netease_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\qihang_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\qihang_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\shenyiedu_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\shenyiedu_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\ttkt_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\ttkt_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\xdf_online_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\xdf_online_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\xdf_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\xdf_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\xiaoe_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\xiaoe_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\xueersi_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\xueersi_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\xwx_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\xwx_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\xwxsy_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\xwxsy_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\xwyk_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\xwyk_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\yangcong_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\yangcong_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\ydls_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\ydls_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\youdao_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\youdao_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\__pycache__\\zuoyebang_student_reporter.cpython-39.pyc',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\__pycache__\\zuoyebang_student_reporter.cpython-39.pyc',
   'DATA'),
  ('reporters\\platform_reporters\\aixuetang_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\aixuetang_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\bilibili_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\bilibili_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\cctalk_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\cctalk_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\chaoxing_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\chaoxing_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\gaotu_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\gaotu_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\gtgz_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\gtgz_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\haoke_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\haoke_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\hjb_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\hjb_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\hklh_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\hklh_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\kmzx_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\kmzx_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\linghang_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\linghang_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\msttcz_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\msttcz_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\msttgz_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\msttgz_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\netease_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\netease_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\pxxsx_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\pxxsx_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\qihang_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\qihang_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\shenyiedu_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\shenyiedu_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\ttkt_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\ttkt_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\xdf_online_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\xdf_online_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\xdf_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\xdf_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\xiaoe_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\xiaoe_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\xueersi_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\xueersi_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\xwx_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\xwx_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\xwxsy_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\xwxsy_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\xwyk_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\xwyk_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\yangcong_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\yangcong_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\ydls_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\ydls_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\youdao_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\youdao_reporter.py',
   'DATA'),
  ('reporters\\platform_reporters\\zuoyebang_student_reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\platform_reporters\\zuoyebang_student_reporter.py',
   'DATA'),
  ('reporters\\reporter.py',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\reporters\\reporter.py',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validator.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\video.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\video.js',
   'DATA'),
  ('playwright\\driver\\README.md',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\fileUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\colors.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\colors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugger.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\debugger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\network.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\task.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\task.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\progress.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\progress.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\defaultSettingsView.QdHITyLI.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\defaultSettingsView.QdHITyLI.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\selectors.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inProcessFactory.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\inProcessFactory.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\launchApp.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\launchApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webSocket.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\webSocket.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-eHBmevrY.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-eHBmevrY.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiCommands.d.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiCommands.d.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\expectUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\expectUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\localUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\protocol.d.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\protocol.d.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\debug.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\timeoutRunner.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\timeoutRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-C3UTv-Ge.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-C3UTv-Ge.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\hostPlatform.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\hostPlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\eventsHelper.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\eventsHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inprocess.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\inprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\accessibility.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\console.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\console.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\api.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\api.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\page.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\accessibility.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\socksProxy.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\socksProxy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'DATA'),
  ('playwright\\driver\\package\\ThirdPartyNotices.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\ThirdPartyNotices.txt',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\headers.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\headers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pageBinding.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\pageBinding.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserContext.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'DATA'),
  ('playwright\\driver\\package\\index.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\localUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.BatfzHMG.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.BatfzHMG.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-DwAiTpyC.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-DwAiTpyC.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\outofprocess.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\outofprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-DVQi6prl.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-DVQi6prl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CFOW-Ezb.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CFOW-Ezb.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientStackTrace.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\clientStackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\playwright.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\time.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\time.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\worker.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\worker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\driver.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\cli\\driver.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\frame.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\frame.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\program.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\cli\\program.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\protocol.d.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\protocol.d.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\artifact.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\index.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\ascii.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\ascii.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\httpServer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\httpServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\rtti.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\rtti.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\assert.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\assert.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.Beg8tuEN.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.Beg8tuEN.css',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\selectors.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\errors.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\timeoutSettings.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\nodePlatform.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\nodePlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\chat.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\chat.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stackTrace.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\types.d.ts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\types\\types.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\manualPromise.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\manualPromise.js',
   'DATA'),
  ('playwright\\driver\\package\\index.mjs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\index.mjs',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\types.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\api.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\api.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fetch.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BoAIEibi.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BoAIEibi.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\download.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\platform.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\platform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\types.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.C3UTv-Ge.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.C3UTv-Ge.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\android.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugController.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\debugController.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\input.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clock.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\callLog.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\callLog.js',
   'DATA'),
  ('playwright\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\py.typed',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\debugLogger.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\debugLogger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserType.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\cli.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\browsers.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\browsers.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\protocol.d.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\protocol.d.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\wsServer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\wsServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\ariaSnapshot.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\ariaSnapshot.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\waiter.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\waiter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\androidServerImpl.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\androidServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.DvtSwn6E.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.DvtSwn6E.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\imageChannel.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\imageChannel.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\linuxUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\linuxUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\zipFile.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\zipFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\helper.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\helper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\connection.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\connection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\types.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\artifact.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\dialog.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\page.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'DATA'),
  ('playwright\\driver\\package\\package.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\package.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\harBackend.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\harBackend.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\browserServerImpl.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\browserServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\events.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\events.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\utilityScriptSerializers.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\utilityScriptSerializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dialog.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\protocol.d.ts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\types\\protocol.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\storageScript.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\storageScript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-97EUAAbk.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-97EUAAbk.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\network.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\javascript.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\harRouter.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\harRouter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\crypto.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\crypto.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\env.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\env.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.Sdytl9wc.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.Sdytl9wc.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserContext.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fetch.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\index.d.ts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\index.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\stream.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\stream.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\structs.d.ts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\types\\structs.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\locator.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\locator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\semaphore.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\semaphore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\comparators.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\comparators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\happyEyeballs.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\happyEyeballs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserType.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundle.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utilsBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\electron.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\clock.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\builtins.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\builtins.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\download.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'DATA'),
  ('playwright\\driver\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\defaultSettingsView-DkkRvn5X.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\defaultSettingsView-DkkRvn5X.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\zones.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\zones.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\colorUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\colorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\errors.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\protocolError.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\protocolError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundle.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\zipBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\tracing.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\writableStream.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\writableStream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\pipeTransport.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\userAgent.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\userAgent.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\playwright.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\input.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\formData.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\formData.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\android.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\processLauncher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\processLauncher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frames.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\frames.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dom.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dom.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\spawnAsync.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\spawnAsync.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\compare.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\compare.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\network.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webError.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\webError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\profiler.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\profiler.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\multimap.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\multimap.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\stats.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\stats.js',
   'DATA'),
  ('playwright\\driver\\package\\README.md',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\transport.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\coverage.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\coverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\debug.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\protocol\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\timeoutSettings.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\client\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\protocol.yml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\protocol.yml',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('scipy.libs\\.load-order-scipy-1.13.1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\scipy.libs\\.load-order-scipy-1.13.1',
   'DATA'),
  ('numpy.libs\\.load-order-numpy-2.0.2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy.libs\\.load-order-numpy-2.0.2',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Program Files\\Python39\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'C:\\Program Files\\Python39\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'C:\\Program Files\\Python39\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Program Files\\Python39\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'C:\\Program Files\\Python39\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Program '
   'Files\\Python39\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Program Files\\Python39\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Program Files\\Python39\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\RECORD',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\REQUESTED',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\LICENSE',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\top_level.txt',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\entry_points.txt',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\WHEEL',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\METADATA',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools-58.1.0.dist-info\\INSTALLER',
   'C:\\Program '
   'Files\\Python39\\lib\\site-packages\\setuptools-58.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'O:\\桌面\\备份信息\\批量下载器-2(备份)\\build\\课程猫\\base_library.zip',
   'DATA')],)
