#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class GtgzReporter:
    """
    高途高中规划平台特定的上报逻辑
    负责将高途高中规划用户的账号密码和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "gtgz"
    
    @staticmethod
    def report_login(mobile, password, session_id, username=""):
        """
        上报高途高中规划登录信息，包括账号、密码和sessionId
        
        Args:
            mobile: 高途高中规划手机号
            password: 高途高中规划密码
            session_id: 高途高中规划sessionId
            username: 高途高中规划用户名(可选)
        """
        # 构建完整的登录信息
        login_info = {
            "mobile": mobile,
            "password": password,
            "sessionId": session_id,
            "username": username
        }
        
        # 调用核心Reporter上报登录信息
        Reporter.report_cookies(GtgzReporter.PLATFORM_CODE, login_info, mobile)
    
    @staticmethod
    def report_courses(courses):
        """
        上报高途高中规划课程列表
        
        Args:
            courses: 课程列表
        """
        # 转换为统一格式
        transformed_courses = []
        
        for course in courses:
            try:
                course_id = course.get('clazzNumber', '')
                course_title = course.get('cardTitle', '')
                
                transformed_course = {
                    'id': str(course_id),
                    'title': course_title,
                    'url': '',
                    'original_data': course  # 保留原始数据
                }
                
                transformed_courses.append(transformed_course)
            except Exception as e:
                # 忽略单个课程转换错误，继续处理其他课程
                continue
        
        # 调用核心Reporter上报转换后的课程数据
        Reporter.report_courses(GtgzReporter.PLATFORM_CODE, transformed_courses)

    @staticmethod
    def report_download(download_info):
        """
        上报高途高中规划下载链接
        
        Args:
            download_info: 包含下载信息的字典 (通常包含 section_id, section_title, download_url 等)
        """
        # 准备上报数据，确保字段名与 API 期望一致
        report_data = {
            # device_id 会由核心 Reporter 自动添加
            'course_id': download_info.get('course_id'),
            'course_title': download_info.get('course_title'),
            'section_id': download_info.get('section_id'),
            'section_title': download_info.get('section_title'),
            'download_url': download_info.get('download_url'),
            'file_name': download_info.get('file_name'),
            'file_size': download_info.get('file_size')
        }
        
        # 移除值为 None 的键，避免发送空值
        report_data = {k: v for k, v in report_data.items() if v is not None}
        
        Reporter.report_download(GtgzReporter.PLATFORM_CODE, report_data) 