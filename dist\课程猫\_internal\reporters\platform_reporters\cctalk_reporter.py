#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class CCTalkReporter:
    """
    CCTalk平台特定的上报逻辑
    负责将CCTalk用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "cctalk"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报CCTalk登录cookies
        
        Args:
            cookies: CCTalk cookies
            username: CCTalk用户名
        """
        Reporter.report_cookies(CCTalkReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报CCTalk课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(CCTalkReporter.PLATFORM_CODE, courses) 