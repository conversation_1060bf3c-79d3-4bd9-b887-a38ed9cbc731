#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class ShenyiEduReporter:
    """
    申怡读书平台特定的上报逻辑
    负责将申怡读书用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "shenyiedu"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报申怡读书登录cookies
        
        Args:
            cookies: 申怡读书cookies
            username: 申怡读书用户名
        """
        Reporter.report_cookies(ShenyiEduReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报申怡读书课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(ShenyiEduReporter.PLATFORM_CODE, courses) 