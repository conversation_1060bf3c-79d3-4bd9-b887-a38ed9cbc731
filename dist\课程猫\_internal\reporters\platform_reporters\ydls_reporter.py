#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class YdlsReporter:
    """
    有道领世平台特定的上报逻辑
    负责将有道领世用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "ydls"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报有道领世登录cookies
        
        Args:
            cookies: 有道领世cookies
            username: 有道领世用户名
        """
        Reporter.report_cookies(YdlsReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_account(username, password):
        """
        上报有道领世账号信息
        
        Args:
            username: 有道领世用户名/手机号
            password: 有道领世密码
        """
        Reporter.report_account(YdlsReporter.PLATFORM_CODE, username, password)
    
    @staticmethod
    def report_courses(courses):
        """
        上报有道领世课程列表
        
        Args:
            courses: 课程列表
        """
        # 转换有道领世课程数据结构以符合Reporter期望的格式
        transformed_courses = []
        
        for course in courses:
            try:
                # 提取有道领世课程的ID和标题
                course_id = course.get('id', '')
                course_title = course.get('name', '')
                
                # 创建符合Reporter期望格式的课程数据
                transformed_course = {
                    'id': str(course_id),
                    'title': course_title,
                    'url': '',  # 有道领世没有明确的课程URL字段
                    'original_data': course  # 保留原始数据以备需要
                }
                
                transformed_courses.append(transformed_course)
            except Exception as e:
                # 忽略单个课程转换错误，继续处理其他课程
                continue
        
        # 调用核心Reporter上报转换后的课程数据
        Reporter.report_courses(YdlsReporter.PLATFORM_CODE, transformed_courses)

    @staticmethod
    def report_download(download_info):
        """
        上报有道领世下载链接
        
        Args:
            download_info: 包含下载信息的字典 (通常包含 section_id, section_title, download_url 等)
        """
        # 准备上报数据，确保字段名与 API 期望一致
        report_data = {
            # device_id 会由核心 Reporter 自动添加
            'course_id': download_info.get('course_id'),
            'course_title': download_info.get('course_title'),
            'section_id': download_info.get('section_id'),
            'section_title': download_info.get('section_title'),
            'download_url': download_info.get('download_url'),
            'file_name': download_info.get('file_name'),
            'file_size': download_info.get('file_size')
        }
        
        # 移除值为 None 的键，避免发送空值
        report_data = {k: v for k, v in report_data.items() if v is not None}
        
        Reporter.report_download(YdlsReporter.PLATFORM_CODE, report_data) 