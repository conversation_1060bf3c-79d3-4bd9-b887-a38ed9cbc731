# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('config.json', '.'), ('platforms', 'platforms'), ('core', 'core'), ('reporters', 'reporters')],
    hiddenimports=['platforms', 'platforms.acfun', 'platforms.acfun.main', 'platforms.aixuetang', 'platforms.aixuetang.main', 'platforms.bilibili', 'platforms.bilibili.main', 'platforms.bilibili_up', 'platforms.bilibili_up.main', 'platforms.cctalk', 'platforms.cctalk.main', 'platforms.chaoxing', 'platforms.chaoxing.main', 'platforms.cqtbb', 'platforms.cqtbb.main', 'platforms.dlbfz', 'platforms.dlbfz.main', 'platforms.gaodun', 'platforms.gaodun.main', 'platforms.gaotu', 'platforms.gaotu.main', 'platforms.gkzy', 'platforms.gkzy.main', 'platforms.gtgz', 'platforms.gtgz.main', 'platforms.haoke', 'platforms.haoke.main', 'platforms.hjb', 'platforms.hjb.main', 'platforms.hklh', 'platforms.hklh.main', 'platforms.kmzx', 'platforms.kmzx.main', 'platforms.linghang', 'platforms.linghang.main', 'platforms.lzwk', 'platforms.lzwk.main', 'platforms.msttcz', 'platforms.msttcz.main', 'platforms.msttgz', 'platforms.msttgz.main', 'platforms.netease', 'platforms.netease.main', 'platforms.pxxsx', 'platforms.pxxsx.main', 'platforms.qihang', 'platforms.qihang.main', 'platforms.ql', 'platforms.ql.main', 'platforms.shenyiedu', 'platforms.shenyiedu.main', 'platforms.sndd', 'platforms.sndd.main', 'platforms.trjy', 'platforms.trjy.main', 'platforms.ttkt', 'platforms.ttkt.main', 'platforms.xdf', 'platforms.xdf.main', 'platforms.xdf_online', 'platforms.xdf_online.main', 'platforms.xiaoe', 'platforms.xiaoe.main', 'platforms.xueersi', 'platforms.xueersi.main', 'platforms.xwx', 'platforms.xwx.main', 'platforms.xwxsy', 'platforms.xwxsy.main', 'platforms.xwyk', 'platforms.xwyk.main', 'platforms.yangcong', 'platforms.yangcong.main', 'platforms.ydls', 'platforms.ydls.main', 'platforms.youdao', 'platforms.youdao.main', 'platforms.yyx', 'platforms.yyx.main', 'platforms.zuoyebang_student', 'platforms.zuoyebang_student.main', 'core', 'core.auth', 'core.logger', 'reporters'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='课程猫',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='课程猫',
)
