#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class PxxsxReporter:
    """
    平行线数学平台特定的上报逻辑
    负责将平行线数学平台的token和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "pxxsx"
    
    @staticmethod
    def report_login(token, mobile=""):
        """
        上报平行线数学登录token
        
        Args:
            token: 平行线数学token
            mobile: 平行线数学登录手机号(可选)
        """
        # 构建登录信息
        login_info = {
            "token": token,
            "mobile": mobile
        }
        
        # 调用核心Reporter上报登录信息
        Reporter.report_cookies(PxxsxReporter.PLATFORM_CODE, login_info, mobile)
    
    @staticmethod
    def report_courses(courses):
        """
        上报平行线数学课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(PxxsxReporter.PLATFORM_CODE, courses) 