#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class AixuetangReporter:
    """
    爱学堂平台特定的上报逻辑
    负责将爱学堂用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "aixuetang"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报爱学堂登录cookies
        
        Args:
            cookies: 爱学堂cookies
            username: 爱学堂用户名
        """
        Reporter.report_cookies(AixuetangReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报爱学堂课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(AixuetangReporter.PLATFORM_CODE, courses) 