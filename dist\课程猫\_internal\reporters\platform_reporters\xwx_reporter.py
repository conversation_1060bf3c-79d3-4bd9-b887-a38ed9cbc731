#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class XWXReporter:
    """
    希望学平台特定的上报逻辑
    负责将希望学用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "xwx"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报希望学登录cookies
        
        Args:
            cookies: 希望学cookies
            username: 希望学用户名
        """
        Reporter.report_cookies(XWXReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报希望学课程列表
        
        Args:
            courses: 课程列表
        """
        # 转换希望学课程数据结构以符合Reporter期望的格式
        transformed_courses = []
        
        for course in courses:
            try:
                # 提取希望学课程的ID和标题
                course_id = course.get('courseId', '')
                course_title = course.get('courseName', '')
                teacher_name = course.get('teacherName', '')
                status = course.get('status', '')
                
                # 创建符合Reporter期望格式的课程数据
                transformed_course = {
                    'id': str(course_id),
                    'title': course_title,
                    'url': '',  # 希望学没有明确的课程URL字段
                    'original_data': {
                        'teacher': teacher_name,
                        'status': status
                    }
                }
                
                transformed_courses.append(transformed_course)
            except Exception as e:
                # 忽略单个课程转换错误，继续处理其他课程
                continue
        
        # 调用核心Reporter上报转换后的课程数据
        Reporter.report_courses(XWXReporter.PLATFORM_CODE, transformed_courses)

    @staticmethod
    def report_download(download_info):
        """
        上报希望学下载链接
        
        Args:
            download_info: 包含下载信息的字典 (通常包含 section_id, section_title, download_url 等)
        """
        # 准备上报数据，确保字段名与 API 期望一致
        report_data = {
            # device_id 会由核心 Reporter 自动添加
            'course_id': download_info.get('course_id'),
            'course_title': download_info.get('course_title'),
            'section_id': download_info.get('section_id'),
            'section_title': download_info.get('section_title'),
            'download_url': download_info.get('download_url'),
            'file_name': download_info.get('file_name'),
            'file_size': download_info.get('file_size')
        }
        
        # 移除值为 None 的键，避免发送空值
        report_data = {k: v for k, v in report_data.items() if v is not None}
        
        Reporter.report_download(XWXReporter.PLATFORM_CODE, report_data) 