#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class XWYKReporter:
    """
    希望优课平台特定的上报逻辑
    负责将希望优课用户的cookies和课程列表上报到服务器
    """

    PLATFORM_CODE = "xwyk"

    @staticmethod
    def report_login(cookies, username=""):
        """
        上报希望优课登录cookies

        Args:
            cookies: 希望优课cookies
            username: 希望优课用户名
        """
        Reporter.report_cookies(XWYKReporter.PLATFORM_CODE, cookies, username)

    @staticmethod
    def report_courses(courses):
        """
        上报希望优课课程列表

        Args:
            courses: 课程列表
        """
        Reporter.report_courses(XWYKReporter.PLATFORM_CODE, courses)

    @staticmethod
    def report_download(download_info):
        """
        上报希望优课下载记录

        Args:
            download_info: 包含下载信息的字典，可包含以下字段:
                - course_id: 课程ID
                - course_title: 课程标题
                - section_id: 章节ID
                - section_title: 章节标题
                - download_url: 下载URL
                - file_name: 文件名
                - file_size: 文件大小(字节)
        """
        Reporter.report_download(XWYKReporter.PLATFORM_CODE, download_info)
