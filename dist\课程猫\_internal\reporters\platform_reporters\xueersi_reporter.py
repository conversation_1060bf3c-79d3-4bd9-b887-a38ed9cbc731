#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class XueersiReporter:
    """
    学而思网校平台特定的上报逻辑
    负责将学而思网校用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "xueersi"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报学而思网校登录cookies
        
        Args:
            cookies: 学而思网校cookies
            username: 学而思网校用户名
        """
        Reporter.report_cookies(XueersiReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报学而思网校课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(XueersiReporter.PLATFORM_CODE, courses) 