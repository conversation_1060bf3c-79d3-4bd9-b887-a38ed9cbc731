#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class ChaoxingReporter:
    """
    超星学习通平台特定的上报逻辑
    负责将超星学习通用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "chaoxing"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报超星学习通登录cookies
        
        Args:
            cookies: 超星学习通cookies
            username: 超星学习通用户名
        """
        Reporter.report_cookies(ChaoxingReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报超星学习通课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(ChaoxingReporter.PLATFORM_CODE, courses) 