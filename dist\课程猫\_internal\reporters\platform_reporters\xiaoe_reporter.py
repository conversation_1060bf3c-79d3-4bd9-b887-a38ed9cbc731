#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class XiaoEReporter:
    """
    小鹅通平台特定的上报逻辑
    负责将小鹅通用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "xiaoe"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报小鹅通登录cookies
        
        Args:
            cookies: 小鹅通cookies
            username: 小鹅通用户名
        """
        Reporter.report_cookies(XiaoEReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报小鹅通课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(XiaoEReporter.PLATFORM_CODE, courses) 