#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter
import json

class YoudaoReporter:
    """
    有道平台(包括有道素养和有道精品课)特定的上报逻辑
    负责将有道平台用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "youdao"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报有道平台登录cookies
        
        Args:
            cookies: 有道平台cookies（可能是数组、字典或字符串）
            username: 有道平台用户名
        """
        # 处理cookies格式，确保服务器能正确识别
        if isinstance(cookies, list):
            # 将cookie数组转换为字典，使用name/value对
            cookie_dict = {}
            for cookie in cookies:
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    cookie_dict[cookie['name']] = cookie['value']
            
            # 使用转换后的字典进行上报
            #print(f"上报有道素养cookie，共 {len(cookie_dict)} 个键值对")
            Reporter.report_cookies(YoudaoReporter.PLATFORM_CODE, cookie_dict, username)
        else:
            # 如果不是列表，直接上报
            Reporter.report_cookies(YoudaoReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报有道平台课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(YoudaoReporter.PLATFORM_CODE, courses) 