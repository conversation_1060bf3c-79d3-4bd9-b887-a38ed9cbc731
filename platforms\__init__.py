#!/usr/bin/env python
# -*- coding: utf-8 -*-



"""
平台模块聚合
负责聚合所有平台的下载器
"""

import logging
import requests
from core.logger import Logger
from core.auth import Auth

# 配置平台模块的日志
Logger.setup_logger("platforms")

# 服务器API地址
API_SERVER = "https://k12.kechengmao.top/api"

def get_platform_status():
    """
    从服务器获取平台状态
    
    Returns:
        dict: 平台代码到状态(0/1)的映射，如果验证失败则返回空字典
    """
    # 默认为空字典，表示无法获取平台状态
    platform_status = {}
    
    try:
        # 获取设备ID和授权码
        device_id = Auth.get_device_id()
        license_key = Auth.get_license_key()
        
        if not device_id or not license_key:
            logging.warning("获取平台状态失败: 设备ID或授权码为空")
            return platform_status
        
        # 请求服务器获取平台状态
        response = requests.post(
            f"{API_SERVER}/verify.php",
            json={
                "device_id": device_id,
                "license_key": license_key,
                "get_platform_status": True  # 特殊标记，请求包含平台状态
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == True and "platform_status" in result.get("data", {}):
                platform_status = result["data"]["platform_status"]
                logging.info(f"成功获取平台状态: {len(platform_status)}个平台")
                return platform_status
            else:
                logging.warning(f"获取平台状态失败: {result.get('message', '未知错误')}")
                return platform_status
        else:
            logging.warning(f"获取平台状态HTTP错误: {response.status_code}")
            return platform_status
    except Exception as e:
        logging.error(f"获取平台状态时出错: {str(e)}")
        return platform_status

def get_platform_downloaders():
    """获取所有可用的平台下载器 - 增强版"""
    # 添加打包环境调试信息
    import sys
    import os
    
    # 记录环境信息
    if getattr(sys, 'frozen', False):
        logging.info("当前运行在打包环境中")
        # 记录搜索路径
        logging.info(f"Python路径: {sys.path}")
        # 记录当前目录文件
        try:
            app_path = os.path.dirname(sys.executable)
            logging.info(f"应用程序目录: {app_path}")
            if os.path.exists(app_path):
                logging.info(f"应用目录内容: {os.listdir(app_path)}")
            
            # 检查platforms目录
            platforms_path = os.path.join(app_path, 'platforms')
            if os.path.exists(platforms_path):
                logging.info(f"platforms目录内容: {os.listdir(platforms_path)}")
        except Exception as e:
            logging.error(f"检查目录结构时出错: {e}")
    else:
        logging.info("当前运行在常规Python环境中")
    """获取所有可用的平台下载器 - 增强版"""
    # 添加打包环境调试信息
    import sys
    import os
    
    # 记录环境信息
    if getattr(sys, 'frozen', False):
        logging.info("当前运行在打包环境中")
        # 记录搜索路径
        logging.info(f"Python路径: {sys.path}")
        # 记录当前目录文件
        try:
            app_path = os.path.dirname(sys.executable)
            logging.info(f"应用程序目录: {app_path}")
            if os.path.exists(app_path):
                logging.info(f"应用目录内容: {os.listdir(app_path)}")
            
            # 检查platforms目录
            platforms_path = os.path.join(app_path, 'platforms')
            if os.path.exists(platforms_path):
                logging.info(f"platforms目录内容: {os.listdir(platforms_path)}")
        except Exception as e:
            logging.error(f"检查目录结构时出错: {e}")
    else:
        logging.info("当前运行在常规Python环境中")
    """
    获取所有可用的平台下载器
    
    Returns:
        dict: 平台名称到下载器类的映射
    """
    downloaders = {}
    
    # 获取平台状态 - 严格通过线上验证
    platform_status = get_platform_status()
    
    # 如果无法获取平台状态，不加载任何平台
    if not platform_status:
        logging.warning("无法获取平台状态，不加载任何平台下载器")
        return downloaders
    
    logging.info(f"获取到平台状态: {platform_status}")
    
    # 定义平台代码到名称的映射
    platform_codes = {
        "bilibili": "B站",
        "netease": "网易云课堂",
        "shenyiedu": "申怡读书",
        "gaotu": "高途",
        "cctalk": "CCTalk",
        "xdf": "新东方云教师",
        "acfun": "A站",
        "yangcong": "洋葱学园",
        "xiaoe": "小鹅通",
        "qihang": "启航教育",
        "aixuetang": "爱学堂",
        "xueersi": "学而思网校",
        "chaoxing": "超星学习通",
        "youdao": "有道素养",
        "xdf_online": "新东方在线",
        "haoke": "好课在线",
        "zuoyebang_linghang": "作业帮领航",
        "zuoyebang_student": "作业帮学生端",
        "kmzx": "开明致学",
        "gtgz": "高途高中规划",
        "ydls": "有道领世",
        "xwx": "希望学",
        "xwxsy": "希望学素养",
        "xwyk": "希望优课",
        "ttkt": "途途课堂",
        "ql": "千聊",
        "yyx": "云优学",
        "lzwk": "荔枝微课",
        "pxxsx": "平行线数学",
        "gkzy": "赶考状元",
        "hjb": "火箭班",
        "hklh": "好课领航",
        "msttcz": "名师天团_初中部",
        "msttgz": "名师天团_高中部",
        "sndd": "少年得到",
        "cqtbb": "常青藤爸爸",
        "dlbfz": "地理八分钟",
        "trjy": "天任教育"
    }
    
    # 导入B站下载器
    try:
        # 检查平台状态，只有状态为1(启用)才加载
        if platform_status.get("bilibili", 0) == 1:
            from platforms.bilibili.downloader import BilibiliDownloader
            downloaders["B站"] = BilibiliDownloader
        else:
            logging.info("B站平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入B站下载器失败: {str(e)}")
    
    # 导入B站UP主下载器 - 特殊情况，依赖B站平台状态
    try:
        if platform_status.get("bilibili", 0) == 1:
            from platforms.bilibili_up.downloader import BiliBiliUPDownloader
            downloaders["B站UP主"] = BiliBiliUPDownloader
        else:
            logging.info("B站平台已被禁用，跳过加载B站UP主下载器")
    except ImportError as e:
        logging.warning(f"导入B站UP主下载器失败: {str(e)}")
    
    # 导入网易云课堂下载器
    try:
        if platform_status.get("netease", 0) == 1:
            from platforms.netease.downloader import NeteaseDownloader
            downloaders["网易云课堂"] = NeteaseDownloader
        else:
            logging.info("网易云课堂平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入网易云课堂下载器失败: {str(e)}")
    
    # 导入申怡读书下载器
    try:
        if platform_status.get("shenyiedu", 0) == 1:
            from platforms.shenyiedu.downloader import ShenyiDownloader
            downloaders["申怡读书"] = ShenyiDownloader
        else:
            logging.info("申怡读书平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入申怡读书下载器失败: {str(e)}")
        
    # 导入高途下载器
    try:
        if platform_status.get("gaotu", 0) == 1:
            from platforms.gaotu.downloader import GaotuDownloader
            downloaders["高途"] = GaotuDownloader
        else:
            logging.info("高途平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入高途下载器失败: {str(e)}")
        
    # 导入CCTalk下载器
    try:
        if platform_status.get("cctalk", 0) == 1:
            from platforms.cctalk.downloader import CCTalkDownloader
            downloaders["CCTalk"] = CCTalkDownloader
        else:
            logging.info("CCTalk平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入CCTalk下载器失败: {str(e)}")
    
    # 导入新东方云教师下载器
    try:
        if platform_status.get("xdf", 0) == 1:
            from platforms.xdf.downloader import XDFDownloader
            downloaders["新东方云教师"] = XDFDownloader
        else:
            logging.info("新东方云教师平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入新东方云教师下载器失败: {str(e)}")
    
    # 导入A站下载器
    try:
        if platform_status.get("acfun", 0) == 1:
            from platforms.acfun.downloader import AcFunDownloader
            downloaders["A站"] = AcFunDownloader
        else:
            logging.info("A站平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入A站下载器失败: {str(e)}")
    
    # 导入洋葱学园下载器
    try:
        if platform_status.get("yangcong", 0) == 1:
            from platforms.yangcong.downloader import YangCongDownloader
            downloaders["洋葱学园"] = YangCongDownloader
        else:
            logging.info("洋葱学园平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入洋葱学园下载器失败: {str(e)}")
    
    # 导入小鹅通下载器
    try:
        if platform_status.get("xiaoe", 0) == 1:
            from platforms.xiaoe.downloader import XiaoEDownloader
            downloaders["小鹅通"] = XiaoEDownloader
        else:
            logging.info("小鹅通平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入小鹅通下载器失败: {str(e)}")
        
    # 导入启航教育下载器
    try:
        if platform_status.get("qihang", 0) == 1:
            from platforms.qihang.downloader import QiHangDownloader
            downloaders["启航教育"] = QiHangDownloader
        else:
            logging.info("启航教育平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入启航教育下载器失败: {str(e)}")
    
    # 导入爱学堂下载器
    try:
        if platform_status.get("aixuetang", 0) == 1:
            from platforms.aixuetang.downloader import AiXueTangDownloader
            downloaders["爱学堂"] = AiXueTangDownloader
        else:
            logging.info("爱学堂平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入爱学堂下载器失败: {str(e)}")
        
    # 导入学而思网校下载器
    try:
        if platform_status.get("xueersi", 0) == 1:
            from platforms.xueersi.downloader import XueErSiDownloader
            downloaders["学而思网校"] = XueErSiDownloader
        else:
            logging.info("学而思网校平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入学而思网校下载器失败: {str(e)}")
    
    # 导入超星学习通下载器
    try:
        if platform_status.get("chaoxing", 0) == 1:
            from platforms.chaoxing.downloader import ChaoxingDownloader
            downloaders["超星学习通"] = ChaoxingDownloader
        else:
            logging.info("超星学习通平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入超星学习通下载器失败: {str(e)}")
    
    # 导入有道素养下载器
    try:
        if platform_status.get("youdao", 0) == 1:
            from platforms.youdao.downloader import YoudaoDownloader
            downloaders["有道素养"] = YoudaoDownloader
        else:
            logging.info("有道素养平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入有道素养下载器失败: {str(e)}")
    
    # 导入新东方在线下载器
    try:
        if platform_status.get("xdf_online", 0) == 1:
            from platforms.xdf_online.downloader import XDFOnlineDownloader
            downloaders["新东方在线"] = XDFOnlineDownloader
        else:
            logging.info("新东方在线平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入新东方在线下载器失败: {str(e)}")
    
    # 导入好课在线下载器
    try:
        if platform_status.get("haoke", 0) == 1:
            from platforms.haoke.main import HaokeMain
            downloaders["好课在线"] = HaokeMain
        else:
            logging.info("好课在线平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入好课在线下载器失败: {str(e)}")
    
    # 导入作业帮领航下载器
    try:
        if platform_status.get("zuoyebang_linghang", 0) == 1:
            from platforms.linghang.main import LinghangMain
            downloaders["作业帮领航"] = LinghangMain
        else:
            logging.info("作业帮领航平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入作业帮领航下载器失败: {str(e)}")

    # 导入作业帮学生端下载器
    try:
        if platform_status.get("zuoyebang_student", 0) == 1:
            from platforms.zuoyebang_student.main import ZuoyebangStudentMain
            downloaders["作业帮学生端"] = ZuoyebangStudentMain
        else:
            logging.info("作业帮学生端平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入作业帮学生端下载器失败: {str(e)}")

    # 导入好课领航下载器
    try:
        if platform_status.get("hklh", 0) == 1:
            from platforms.hklh.main import HklhMain
            downloaders["好课领航"] = HklhMain
        else:
            logging.info("好课领航平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入好课领航下载器失败: {str(e)}")
    
    # 导入开明致学下载器
    try:
        if platform_status.get("kmzx", 0) == 1:
            from platforms.kmzx.main import KMZXMain
            downloaders["开明致学"] = KMZXMain
        else:
            logging.info("开明致学平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入开明致学下载器失败: {str(e)}")
    
    # 导入高途高中规划下载器
    try:
        if platform_status.get("gtgz", 0) == 1:
            from platforms.gtgz.main import GtgzMain
            downloaders["高途高中规划"] = GtgzMain
        else:
            logging.info("高途高中规划平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入高途高中规划下载器失败: {str(e)}")
        
    # 导入有道领世下载器
    try:
        if platform_status.get("ydls", 0) == 1:
            from platforms.ydls.main import YdlsMain
            downloaders["有道领世"] = YdlsMain
        else:
            logging.info("有道领世平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入有道领世下载器失败: {str(e)}")
    
    # 导入希望学平台
    try:
        if platform_status.get("xwx", 0) == 1:
            from platforms.xwx.main import XWXMain
            downloaders["希望学"] = XWXMain
            logging.info("希望学平台加载成功")
        else:
            logging.info("希望学平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入希望学下载器失败: {str(e)}")
    
    # 导入希望学素养平台
    try:
        if platform_status.get("xwxsy", 0) == 1:
            from platforms.xwxsy.main import XWXSYMain
            downloaders["希望学素养"] = XWXSYMain
            logging.info("希望学素养平台加载成功")
        else:
            logging.info("希望学素养平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入希望学素养下载器失败: {str(e)}")

    # 导入希望优课平台
    try:
        if platform_status.get("xwyk", 0) == 1:
            from platforms.xwyk.main import XWYKMain
            downloaders["希望优课"] = XWYKMain
            logging.info("希望优课平台加载成功")
        else:
            logging.info("希望优课平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入希望优课下载器失败: {str(e)}")
    
    # 导入途途课堂平台
    try:
        if platform_status.get("ttkt", 0) == 1:
            from platforms.ttkt.main import TtktMain
            downloaders["途途课堂"] = TtktMain
            logging.info("途途课堂平台加载成功")
        else:
            logging.info("途途课堂平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入途途课堂下载器失败: {str(e)}")
    
    # 导入千聊平台
    try:
        if platform_status.get("ql", 0) == 1:
            from platforms.ql.main import QLMain
            downloaders["千聊"] = QLMain
            logging.info("千聊平台加载成功")
        else:
            logging.info("千聊平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入千聊下载器失败: {str(e)}")
    
    # 导入云优学平台
    try:
        if platform_status.get("yyx", 0) == 1:
            from platforms.yyx.main import YYXMain
            downloaders["云优学"] = YYXMain
            logging.info("云优学平台加载成功")
        else:
            logging.info("云优学平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入云优学下载器失败: {str(e)}")
    
    # 导入荔枝微课平台
    try:
        if platform_status.get("lzwk", 0) == 1:
            from platforms.lzwk.main import LZWKMain
            downloaders["荔枝微课"] = LZWKMain
            logging.info("荔枝微课平台加载成功")
        else:
            logging.info("荔枝微课平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入荔枝微课下载器失败: {str(e)}")
    
    # 导入平行线数学平台
    try:
        if platform_status.get("pxxsx", 0) == 1:
            from platforms.pxxsx.downloader import PxxsxDownloader
            downloaders["平行线数学"] = PxxsxDownloader
            logging.info("平行线数学平台加载成功")
        else:
            logging.info("平行线数学平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入平行线数学下载器失败: {str(e)}")
    
    # 导入赶考状元平台
    try:
        if platform_status.get("gkzy", 0) == 1:
            from platforms.gkzy.main import GKZYMain
            downloaders["赶考状元"] = GKZYMain
            logging.info("赶考状元平台加载成功")
        else:
            logging.info("赶考状元平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入赶考状元下载器失败: {str(e)}")    
    # 导入火箭班平台
    try:
        if platform_status.get("hjb", 0) == 1:
            from platforms.hjb.main import HJBMain
            downloaders["火箭班"] = HJBMain
            logging.info("火箭班平台加载成功")
        else:
            logging.info("火箭班平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入火箭班下载器失败: {str(e)}")
    
    # 导入名师天团_初中部平台
    try:
        if platform_status.get("msttcz", 0) == 1:
            from platforms.msttcz.main import MSttczMain
            downloaders["名师天团_初中部"] = MSttczMain
            logging.info("名师天团_初中部平台加载成功")
        else:
            logging.info("名师天团_初中部平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入名师天团_初中部下载器失败: {str(e)}")
    
    # 导入名师天团_高中部平台
    try:
        if platform_status.get("msttgz", 0) == 1:
            from platforms.msttgz.main import MSttgzMain
            downloaders["名师天团_高中部"] = MSttgzMain
            logging.info("名师天团_高中部平台加载成功")
        else:
            logging.info("名师天团_高中部平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入名师天团_高中部下载器失败: {str(e)}")
    
    # 导入少年得到下载器
    try:
        if platform_status.get("sndd", 0) == 1:
            from platforms.sndd.main import SNDDMain
            downloaders["少年得到"] = SNDDMain
            logging.info("少年得到平台加载成功")
        else:
            logging.info("少年得到平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入少年得到下载器失败: {str(e)}")
    
    # 导入常青藤爸爸下载器
    try:
        if platform_status.get("cqtbb", 0) == 1:
            from platforms.cqtbb.main import CQTBBMain
            downloaders["常青藤爸爸"] = CQTBBMain
            logging.info("常青藤爸爸平台加载成功")
        else:
            logging.info("常青藤爸爸平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入常青藤爸爸下载器失败: {str(e)}")
    
    # 导入地理八分钟下载器
    try:
        if platform_status.get("dlbfz", 0) == 1:
            from platforms.dlbfz.main import DLBFZMain
            downloaders["地理八分钟"] = DLBFZMain
            logging.info("地理八分钟平台加载成功")
        else:
            logging.info("地理八分钟平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入地理八分钟下载器失败: {str(e)}")
    
    # 导入天任教育下载器
    try:
        if platform_status.get("trjy", 0) == 1:
            from platforms.trjy.main import TRJYMain
            downloaders["天任教育"] = TRJYMain
            logging.info("天任教育平台加载成功")
        else:
            logging.info("天任教育平台已被禁用，跳过加载")
    except ImportError as e:
        logging.warning(f"导入天任教育下载器失败: {str(e)}")
    
    logging.info(f"成功加载了 {len(downloaders)} 个平台的下载器")
    return downloaders 