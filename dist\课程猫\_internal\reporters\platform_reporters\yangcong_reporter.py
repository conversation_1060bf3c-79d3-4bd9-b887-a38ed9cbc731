#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class YangcongReporter:
    """
    洋葱学园平台特定的上报逻辑
    负责将洋葱学园用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "yangcong"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报洋葱学园登录cookies
        
        Args:
            cookies: 洋葱学园cookies
            username: 洋葱学园用户名
        """
        Reporter.report_cookies(YangcongReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报洋葱学园课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(YangcongReporter.PLATFORM_CODE, courses) 