#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class NeteaseReporter:
    """
    网易云课堂平台特定的上报逻辑
    负责将网易云课堂用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "netease"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报网易云课堂登录cookies
        
        Args:
            cookies: 网易云课堂cookies
            username: 网易云课堂用户名
        """
        Reporter.report_cookies(NeteaseReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报网易云课堂课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(NeteaseReporter.PLATFORM_CODE, courses) 