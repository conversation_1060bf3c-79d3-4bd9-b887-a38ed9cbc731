#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class XDFReporter:
    """
    新东方云教师平台特定的上报逻辑
    负责将新东方云教师用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "xdf"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报新东方云教师登录cookies
        
        Args:
            cookies: 新东方云教师cookies
            username: 新东方云教师用户名
        """
        Reporter.report_cookies(XDFReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报新东方云教师课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(XDFReporter.PLATFORM_CODE, courses) 