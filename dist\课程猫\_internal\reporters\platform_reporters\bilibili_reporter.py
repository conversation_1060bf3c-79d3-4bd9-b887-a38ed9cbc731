#!/usr/bin/env python
# -*- coding: utf-8 -*-

from reporters.reporter import Reporter

class BilibiliReporter:
    """
    B站平台特定的上报逻辑
    负责将B站用户的cookies和课程列表上报到服务器
    """
    
    PLATFORM_CODE = "bilibili"
    
    @staticmethod
    def report_login(cookies, username=""):
        """
        上报B站登录cookies
        
        Args:
            cookies: B站cookies
            username: B站用户名
        """
        Reporter.report_cookies(BilibiliReporter.PLATFORM_CODE, cookies, username)
    
    @staticmethod
    def report_courses(courses):
        """
        上报B站课程列表
        
        Args:
            courses: 课程列表
        """
        Reporter.report_courses(BilibiliReporter.PLATFORM_CODE, courses) 